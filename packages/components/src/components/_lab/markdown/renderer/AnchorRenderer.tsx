import { use<PERSON><PERSON><PERSON> } from './RendererProvider'

export interface AnchorRendererProps {
  href?: string
  children?: React.ReactNode
}

export function AnchorRenderer({ href: url, children }: AnchorRendererProps) {
  const href = defaultUrlTransform(url)

  const { rendererOptions } = useRenderer()

  const onLinkClick = rendererOptions?.anchor?.onClick

  const handleClick = (event: React.MouseEvent<HTMLAnchorElement>) => {
    if (onLinkClick) {
      event.preventDefault()
      onLinkClick(href, event)
    }
  }

  return (
    <a href={href} target='_blank' rel='noreferrer' onClick={handleClick}>
      {children}
    </a>
  )
}

const safeProtocol = /^(https?|ircs?|mailto|xmpp)$/i

/**
 * Make a URL safe.
 *
 * This follows how GitHub works.
 * It allows the protocols `http`, `https`, `irc`, `ircs`, `mailto`, and `xmpp`,
 * and URLs relative to the current protocol (such as `/something`).
 *
 * @satisfies {UrlTransform}
 * @param {string} value
 *   URL.
 * @returns {string}
 *   Safe URL.
 */
export function defaultUrlTransform(value: string) {
  // Same as:
  // <https://github.com/micromark/micromark/blob/929275e/packages/micromark-util-sanitize-uri/dev/index.js#L34>
  // But without the `encode` part.
  const colon = value.indexOf(':')
  const questionMark = value.indexOf('?')
  const numberSign = value.indexOf('#')
  const slash = value.indexOf('/')

  if (
    // If there is no protocol, it's relative.
    colon === -1 ||
    // If the first colon is after a `?`, `#`, or `/`, it's not a protocol.
    (slash !== -1 && colon > slash) ||
    (questionMark !== -1 && colon > questionMark) ||
    (numberSign !== -1 && colon > numberSign) ||
    // It is a protocol, it should be allowed.
    safeProtocol.test(value.slice(0, colon))
  ) {
    return value
  }

  return ''
}
