import {
  Table,
  TableHeader,
  TableBody,
  TableRow,
  TableHead,
  TableCell,
} from '../../_components/Table'

export const TableRenderer = {
  Table: ({ children }: { children?: React.ReactNode }) => (
    <Table
      className='min-w-full w-max max-w-max border border-solid border-[rgba(0,0,0,0.08)] border-collapse border-spacing-0 rounded-[8px] [font-variant:tabular-nums]'
      rootClassName='my-4'
    >
      {children}
    </Table>
  ),
  TableHeader: ({ children }: { children?: React.ReactNode }) => (
    <TableHeader>{children}</TableHeader>
  ),
  TableBody: ({ children }: { children?: React.ReactNode }) => (
    <TableBody>{children}</TableBody>
  ),
  TableRow: ({ children }: { children?: React.ReactNode }) => (
    <TableRow className='border-none bg-white nth-child(2n):bg-[#f5f5f5]'>
      {children}
    </TableRow>
  ),
  TableHead: ({ children }: { children?: React.ReactNode }) => (
    <TableHead className='font-semibold text-left border-none px-[14px] py-[10px] bg-[#f5f5f5]'>
      {children}
    </TableHead>
  ),
  TableCell: ({ children }: { children?: React.ReactNode }) => (
    <TableCell className='min-w-100px max-w-[max(30vw,320px)] px-[14px] py-[10px] border-none text-[#0009]'>
      {children}
    </TableCell>
  ),
}
