import React from 'react'
import type { Root } from 'hast'
import { toJsxRuntime } from 'hast-util-to-jsx-runtime'
import { Fragment, jsx, jsxs } from 'react/jsx-runtime'
import type { HeadingRendererProps } from './HeadingRenderer'
import { HeadingRenderer } from './HeadingRenderer'
import { ParagraphRenderer } from './ParagraphRenderer'
import { TableRenderer } from './TableRenderer'
import {
  ListItemCheckboxRenderer,
  ListItemRenderer,
  ListRenderer,
} from './ListRenderer'
import { StrongRenderer } from './StrongRenderer'
import { AnchorRenderer } from './AnchorRenderer'

function createHeadingComponent(depth: HeadingRendererProps['depth']) {
  return ({ children }: { children: React.ReactNode }) => (
    <HeadingRenderer depth={depth}>{children}</HeadingRenderer>
  )
}

const headingComponents = Object.fromEntries(
  ([1, 2, 3, 4, 5, 6] as const).map(depth => [
    `h${depth}`,
    createHeadingComponent(depth),
  ]),
)

function InnerAstRenderer({ ast }: { ast: Root }) {
  const children = toJsxRuntime(ast, {
    Fragment,
    components: {
      ...headingComponents,

      p: ParagraphRenderer,

      // table
      table: TableRenderer.Table,
      thead: TableRenderer.TableHeader,
      tbody: TableRenderer.TableBody,
      tr: TableRenderer.TableRow,
      th: TableRenderer.TableHead,
      td: TableRenderer.TableCell,

      // list
      ul: ({ children }) => (
        <ListRenderer ordered={false}>{children}</ListRenderer>
      ),
      ol: ({ children }) => <ListRenderer ordered>{children}</ListRenderer>,
      li: ({ className, children }) => (
        <ListItemRenderer checked={className && className === 'task-list-item'}>
          {children}
        </ListItemRenderer>
      ),
      input: ListItemCheckboxRenderer,

      strong: StrongRenderer,

      a: AnchorRenderer,
    },
    ignoreInvalidStyle: true,
    jsx,
    jsxs,
  })

  // const children = nodes.map((node, index) => {
  //   switch (node.type) {
  //     case 'html':
  //       return (
  //         <div
  //           key={`html-${index}`}
  //           dangerouslySetInnerHTML={{ __html: node.value }}
  //         />
  //       )
  //     case 'paragraph':
  //       return <ParagraphRenderer key={`paragraph-${index}`} ast={node} />
  //     case 'list':
  //       return <ListRenderer key={`list-${index}`} ast={node} />
  //     case 'code':
  //       return <CodeRenderer key={`code-${index}`} ast={node} />
  //     case 'blockquote':
  //       return <BlockquoteRenderer key={`blockquote-${index}`} ast={node} />
  //     case 'thematicBreak':
  //       return (
  //         <hr
  //           key={`thematicBreak-${index}`}
  //           className='my-8 border-none h-px bg-[#d1d9e0]'
  //         />
  //       )
  //     case 'math':
  //       return <MathRenderer key={`math-${index}`} ast={node} />
  //     case 'mdxJsxFlowElement':
  //       return <XMLRenderer key={`xml-${index}`} ast={node} />
  //     default:
  //       return null
  //   }
  // })

  return <>{children}</>
}

export const AstRenderer = React.memo(InnerAstRenderer)
