import React from 'react'
import { cva } from 'class-variance-authority'
import { cn } from '@bty/util'

export function ListItemCheckboxRenderer({ checked }: { checked?: boolean }) {
  return (
    <input
      className='!mt-[7.5px] accent-[#7b61ff] pointer-events-none'
      type='checkbox'
      checked={checked}
    />
  )
}

export interface ListItemRendererProps {
  checked?: boolean
  children?: React.ReactNode
}

export function ListItemRenderer({ checked, children }: ListItemRendererProps) {
  const showCheckbox = typeof checked === 'boolean'

  return (
    <li
      className={cn(
        'ps-[0.375em] my-[0.5em] break-words [word-break:break-word]',
        {
          'list-none': showCheckbox,
        },
      )}
    >
      <div className={showCheckbox ? 'flex items-start gap-[8px]' : ''}>
        {children}
      </div>
    </li>
  )
}

const listStyles = cva('mt-0 mb-[1.25em] ps-[1.625em]', {
  variants: {
    listType: {
      ordered: 'list-decimal',
      unordered:
        'list-disc [&_:is(ul)]:list-circle [&_:is(ul)_:is(ul)]:list-square',
    },
  },
  defaultVariants: {
    listType: 'unordered',
  },
})

export interface ListRendererProps {
  ordered?: boolean
  children?: React.ReactNode
}

export function ListRenderer({ ordered, children }: ListRendererProps) {
  const listType = ordered ? 'ordered' : 'unordered'

  return React.createElement(
    ordered ? 'ol' : 'ul',
    {
      className: listStyles({ listType }),
    },
    children,
  )
}
