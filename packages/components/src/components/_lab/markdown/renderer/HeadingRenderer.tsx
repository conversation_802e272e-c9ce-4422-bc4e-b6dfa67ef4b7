import React from 'react'
import type { VariantProps } from 'class-variance-authority'
import { cva } from 'class-variance-authority'

const heading = cva('font-medium', {
  variants: {
    depth: {
      1: 'text-[1.8em] leading-[1.11] mt-0 mb-[0.88em]',
      2: 'text-[1.5em] leading-[1.33] mt-[1.5em] mb-[0.8em]',
      3: 'text-[1.25em] leading-[1.6] mt-[1.6em] mb-[0.6em]',
      4: 'leading-[1.5] mt-[1.5em] mb-[0.5em]',
      5: 'my-0',
      6: 'my-0',
    },
  },
  defaultVariants: {
    depth: 1,
  },
})

export interface HeadingRendererProps extends VariantProps<typeof heading> {
  children: React.ReactNode
  depth: 1 | 2 | 3 | 4 | 5 | 6
}

export function HeadingRenderer({ depth, children }: HeadingRendererProps) {
  return React.createElement(
    `h${depth}`,
    {
      className: heading({ depth }),
    },
    children,
  )
}
