import React, { Fragment } from 'react'
import { useRenderer } from './RendererProvider'

export function StrongRenderer({ children }: { children?: React.ReactNode }) {
  // &nbsp; 用来避免错误换行
  const { renderer } = useRenderer()

  const strong = renderer?.strong

  if (Array.isArray(strong)) {
    const [Strong, props] = strong

    return React.createElement(
      Strong,
      {
        ...props,
      },
      children,
    )
  }

  return (
    <Fragment>
      &nbsp;
      {React.createElement(
        strong || 'strong',
        {
          className: 'font-semibold',
        },
        children,
      )}
      &nbsp;
    </Fragment>
  )
}
