import { subscribeRequest } from '../../client'

export function getScheduleList(page_no: number, page_size: number) {
  return subscribeRequest.post('/v1/automation_task/page', {
    page_no,
    page_size,
  })
}

export function getScheduleDetail(
  id: string,
  page_no: number,
  page_size: number,
) {
  return subscribeRequest.post('/v1/automation_task/records', {
    definition_id: id,
    page_no,
    page_size,
  })
}

export function readScheduleTask(id: string) {
  return subscribeRequest.post('/v1/automation_task/record/read', {
    record_id: id,
  })
}

export function runScheduleTask(id: string) {
  return subscribeRequest.post('/v1/automation_task/execute', {
    definition_id: id,
  })
}
