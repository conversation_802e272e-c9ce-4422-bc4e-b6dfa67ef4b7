import { subscribeRequest } from '../../client'
import type {
  AgentInfo,
  AutomationTaskDetail,
  PageData,
  TaskListItem,
  TeamDetailResponse,
  UpdateAutomationTaskRequest,
} from './next-agent-chat.type'

export function getNextAgentConversationListByBizId(biz_id: string) {
  return subscribeRequest.post('/v1/super_agent/conversations/biz_id', {
    biz_id,
  })
}

export function createNextAgentConversation(
  title: string,
  conversation_type?: string,
  team_id?: string,
) {
  return subscribeRequest.post('/v1/super_agent/chat/create_conversation', {
    title,
    conversation_type,
    team_id,
  })
}

export function getConversationInfo(conversation_id: string) {
  return subscribeRequest.get(
    `/v1/super_agent/chat/get_info/${conversation_id}`,
  )
}

export function updateNextAgentConversation(
  id: string,
  payload: {
    title?: string
    conversation_config?: Record<string, any>
  },
) {
  return subscribeRequest.post('/v1/super_agent/chat/update_conversation', {
    conversation_id: id,
    ...payload,
  })
}

export function generateNextAgentConversationTitle(
  conversation_id: string,
  question: string,
) {
  return subscribeRequest.post('/v1/super_agent/chat/generate_title', {
    conversation_id,
    question,
  })
}

export function deleteNextAgentConversation(conversation_id: string) {
  return subscribeRequest.delete(
    `/v1/super_agent/chat/deleted_conversation/${conversation_id}`,
  )
}

export function getNextAgentMessageList(
  conversation_id: string,
  page_no: number,
  page_size: number,
) {
  return subscribeRequest.post('/v1/super_agent/chat/event_list', {
    conversation_id,
    page_no,
    page_size,
  })
}

export function getNextAgentShareMessageList(
  conversation_id: string,
  page_no: number,
  page_size: number,
) {
  return subscribeRequest.post('/v1/super_agent/chat/event_list_share', {
    conversation_id,
    page_no,
    page_size,
  })
}

export function getNextAgentConversationList(
  page_no: number,
  page_size: number,
) {
  return subscribeRequest.post<PageData<TaskListItem>>(
    '/v1/super_agent/chat/conversation_list',
    {
      page_no,
      page_size,
    },
  )
}

export function getFileUrl(
  taskId: string,
  path: string,
  extra?: Record<string, any>,
): Promise<string> {
  return subscribeRequest.post('/v1/super_agent/chat/oss_url', {
    task_id: taskId,
    file_path: path,
    ...extra,
  })
}

export function getArtifacts(task_id: string): Promise<any[]> {
  return subscribeRequest.post('/v1/super_agent/chat/get_dir_file', {
    task_id,
  })
}

export function getShareFileUrl(taskId: string, path: string): Promise<string> {
  return subscribeRequest.post('/v1/super_agent/chat/oss_url', {
    task_id: taskId,
    file_path: path,
  })
}

export function getShareArtifacts(task_id: string): Promise<any[]> {
  console.log('getShareArtifacts', task_id)
  return subscribeRequest.post('/v1/super_agent/chat/get_dir_file', {
    task_id,
  })
}

// 轮询获取对话任务信息
export function pollConversationIds(conversation_ids: string[]) {
  return subscribeRequest.post(
    '/v1/super_agent/chat/get_conversation_info_list',
    {
      conversation_ids,
    },
  )
}

export function updateConversationPublicStatus(
  conversation_id: string,
  share_status: boolean,
) {
  return subscribeRequest.post('/v1/super_agent/chat/update_public', {
    conversation_id,
    is_public: share_status,
  })
}

// 获取自动化配置详情
export function getAutomationTaskDetail(
  definition_id: string,
): Promise<AutomationTaskDetail> {
  return subscribeRequest.get(`/v1/automation_task/${definition_id}`)
}

// 更新自动化任务配置
export function updateAutomationTask(
  payload: UpdateAutomationTaskRequest,
): Promise<any> {
  return subscribeRequest.post('/v1/automation_task/update', payload)
}

// 删除自动化任务
export function deleteAutomationTask(id: string): Promise<any> {
  return subscribeRequest.delete(`/v1/automation_task/${id}`)
}

export function getAgentTeamList(team_id: string): Promise<AgentInfo[]> {
  return subscribeRequest.get(`/v1/super_agent/team/${team_id}/sub_agent_list`)
}

export async function getAgentTeamDetail(
  team_id: string,
): Promise<TeamDetailResponse> {
  const res = await subscribeRequest.get(
    `/v1/super_agent/team/${team_id}/detail`,
  )
  return res
}

// 创建文件上传记录
export function createFileUploadRecord(payload: {
  file_url: string
  file_type: string
  file_name: string
  file_byte_size: number
  conversation_id: string
}) {
  return subscribeRequest.post(
    '/v1/super_agent/file_upload_record/create',
    payload,
  )
}
