import {
  Button,
  Col,
  Form,
  Input,
  Row,
  Select,
  Table,
  DatePicker,
  Space,
  Cascader,
  Tooltip,
} from 'antd'
import React, { useEffect, useState } from 'react'
import { useRequest } from 'ahooks'
import dayjs from 'dayjs'
import { QuestionCircleOutlined } from '@ant-design/icons'
import { getUserList } from '../../../api/accountManagement'
import { exportExcel } from '../../components/ExportExcel'
import { rangePresets } from '../dataBoard/utils'
import { formatUtcTimeRange } from '../../utils/date'
import type { IFieldType, ITableDataType } from './type'

const { RangePicker } = DatePicker
const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 8 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 16 },
  },
}
const headerMap = {
  id: 'UID',
  username: '账号昵称',
  phone: '手机号码',
  role: '用户角色',
  userEnterprise: '用户企业',
  workspaceNum: '创建空间数',
  agent_count: '创建Agent数',
  ai_count: '创建Flow数',
  demand: '需求描述',
  source: '账号来源',
  channelSource: '渠道推广',
  keyword: '关键词',
  utm_sou: '搜索词',
  create_time: '创建时间',
  loginPageVersion: '注册页版本',
  register_source: '注册页来源链接',
}
const websiteUrl = import.meta.env.VITE_HOME_WEBSITE_URL

function sourceMap(source: string, extraInfo) {
  if (
    source?.includes('aigc') ||
    source?.includes('bing') ||
    source?.includes('baidu')
  ) {
    return `渠道推广(${source})`
  }
  const sourceMap = {
    home: 'PC官网',
    AccountSystem: '后台添加',
    'mobile-home': '手机官网',
    'ai-share': '团队邀请',
    'desktop-app': '桌面应用',
    mindNote: 'MindNote',
    exhibition: '世界人工智能大会',
  }

  if (source === 'ai-share') {
    return `${sourceMap['ai-share']}${extraInfo?.invitedWorkspaceName ? `(${extraInfo?.invitedWorkspaceName})` : ''}`
  }

  if (!source) {
    return '注册页'
  }
  return sourceMap[source] || `其他(${source})`
}
function registerSourceMap(text: string | null) {
  return text ? `${websiteUrl}${text}` : null
}

const fieldMap = {
  source: sourceMap,
  register_source: registerSourceMap,
  create_time: (timeStr: string) =>
    dayjs.utc(timeStr).format('YYYY-MM-DD HH:mm:ss'),
  keyword: (_: string, item: any) => item.utm_term || item.utm_keyword || '',
  channelSource: (source: string) => {
    if (
      source?.includes('aigc') ||
      source?.includes('bing') ||
      source?.includes('baidu')
    ) {
      return source
    }
    return ''
  },
}

// 在组件外部定义级联选择器的选项
const channelOptions = [
  {
    value: 'aigc',
    label: 'aigc',
    children: [
      { value: 'aigc', label: 'aigc' },
      { value: 'aigc02', label: 'aigc02' },
      { value: 'aigc03', label: 'aigc03' },
      { value: 'aigc09', label: 'aigc09' },
    ],
  },
  {
    value: 'bing',
    label: 'bing',
    children: Array.from({ length: 20 }, (_, i) => ({
      value: `bing${(i + 1).toString().padStart(2, '0')}`,
      label: `bing${(i + 1).toString().padStart(2, '0')}`,
    })),
  },
  {
    value: 'baidu',
    label: 'baidu',
    children: Array.from({ length: 20 }, (_, i) => ({
      value: `baidu${(i + 1).toString().padStart(2, '0')}`,
      label: `baidu${(i + 1).toString().padStart(2, '0')}`,
    })),
  },
]

export function processFormValues(values: any) {
  const processedValues: any = {}
  for (const key in values) {
    if (values[key]) {
      if (key === 'create_time') {
        processedValues[key] = formatUtcTimeRange([
          values[key][0],
          values[key][1],
        ])
      } else if (key === 'channelMarketing') {
        if (values[key].length > 0) {
          processedValues[key] = values[key].map(item =>
            item.length > 1 ? item[1] : item[0],
          )
        }
      } else if (key === 'sourceList') {
        if (values[key].length > 0) {
          processedValues[key] = values[key]
        }
      } else {
        processedValues[key] = values[key]
      }
    }
  }
  return processedValues
}

export function AccountManagement() {
  const [form] = Form.useForm()

  const [tableData, setTableData] = useState<ITableDataType>({
    data: [],
    total: 0,
  })
  const {
    run: listRun,
    loading: listLoading,
    data: listData,
  } = useRequest(getUserList, { manual: true })
  const { run: excelRun, loading: excelRunLoading } = useRequest(getUserList, {
    manual: true,
    onSuccess: excelData => {
      if (excelData && excelData.code === 200) {
        exportExcel(excelData.data, headerMap, '用户', '用户管理中心', fieldMap)
      }
    },
  })
  const [searchFormData, setSearchFormData] = useState<IFieldType>({
    current: 1,
    pageSize: 20,
  })
  useEffect(() => {
    listRun(searchFormData)
  }, [searchFormData])
  useEffect(() => {
    if (listData && listData.code === 200) {
      setTableData({ data: listData.data, total: listData.total })
    }
  }, [listData])
  const onFinish = values => {
    const processedValues = processFormValues(values)
    setSearchFormData({
      ...processedValues,
      current: 1,
      pageSize: searchFormData.pageSize,
    })
  }

  const exportExcelBtn = () => {
    const values = form.getFieldsValue()
    const processedValues = processFormValues(values)
    excelRun({
      ...processedValues,
      pageSize: Number.MAX_SAFE_INTEGER,
      current: 1,
    })
  }
  const columns = [
    {
      title: 'UID',
      dataIndex: 'id',
      key: 'id',
    },
    {
      title: '账号昵称',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: '手机号码',
      dataIndex: 'phone',
      key: 'phone',
    },
    {
      title: '用户角色',
      dataIndex: 'role',
      key: 'role',
      render: (text: any) => <label>{text}</label>,
    },
    {
      title: '用户企业',
      dataIndex: 'userEnterprise',
      key: 'userEnterprise',
      render: (text: any) => <label>{text}</label>,
    },
    {
      title: '加入空间 (创建)',
      dataIndex: 'workspaceNum',
      key: 'workspaceNum',
      render: (_, record) => {
        const joinNum = record.joinWorkspaceNum || 0
        const createNum = record.workspaceNum || 0
        if (joinNum === 0 && createNum === 0) {
          return null
        }
        const joinText = joinNum !== 0 ? joinNum.toString() : ''
        const createText = createNum !== 0 ? `(${createNum})` : ''
        return <div>{`${joinText}${createText}`}</div>
      },
    },
    {
      title: '创建应用数',

      dataIndex: 'applicationNum',
      key: 'applicationNum',
      children: [
        {
          title: 'Agent',
          dataIndex: 'agent_count',
          key: 'agent_count',
          render: (text: number) => {
            return text !== 0 ? <div>{text}</div> : null
          },
        },
        {
          title: 'Flow',
          dataIndex: 'ai_count',
          key: 'ai_count',
          render: (text: number) => {
            return text !== 0 ? <div>{text}</div> : null
          },
        },
      ],
    },
    {
      title: '注册来源',
      dataIndex: 'source',
      key: 'source',
      render: (text: string, record) => (
        <label>
          {sourceMap(text, {
            invitedWorkspaceName: record.invitedWorkspaceName,
          })}
          {record?.invitedUrl && (
            <Tooltip title={`邀请链接: ${record.invitedUrl}`}>
              <QuestionCircleOutlined className='ml-4px' />
            </Tooltip>
          )}
        </label>
      ),
    },
    {
      title: '关键词',
      dataIndex: 'utm_term',
      key: 'utm_term',
      render: (_, record) => {
        return record?.utm_term || record?.utm_keyword || ''
      },
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      render: (text: string) => (
        <label>{dayjs.utc(text).format('YYYY-MM-DD HH:mm')}</label>
      ),
    },
    {
      title: '注册来源链接',
      dataIndex: 'register_source',
      key: 'register_source',
      render: (text: string) =>
        text ? (
          <a
            target='_blank'
            href={`${websiteUrl}${text}`}
            rel='noreferrer'
          >{`${websiteUrl}${text}`}</a>
        ) : null,
    },
  ]
  return (
    <div className='bg-neutral-100'>
      <div className='content mt-8 bg-white' style={{ padding: '14px 0' }}>
        <Form
          {...formItemLayout}
          layout='inline'
          form={form}
          onFinish={onFinish}
          style={{ maxWidth: 'none', width: '100%' }}
        >
          <Row gutter={[0, 12]} style={{ width: '100%' }}>
            <Col span={6}>
              <Form.Item<IFieldType> label='账号昵称' name='username'>
                <Input placeholder='请输入' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item<IFieldType> label='手机号码' name='phone'>
                <Input placeholder='请输入' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item<IFieldType> label='UID' name='id'>
                <Input placeholder='请输入' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item<IFieldType> label='创建时间' name='create_time'>
                <RangePicker
                  presets={rangePresets}
                  format={'YYYY-MM-DD'}
                  style={{ width: '100%' }}
                  showTime={false}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item<IFieldType> label='注册来源' name='sourceList'>
                <Select mode='multiple' placeholder='请选择'>
                  <Select.Option value='home'>PC官网</Select.Option>
                  <Select.Option value='mobile-home'>移动端官网</Select.Option>
                  <Select.Option value='channelMarketing'>
                    渠道推广
                  </Select.Option>
                  <Select.Option value='ai-share'>团队邀请</Select.Option>
                  <Select.Option value='desktop-app'>桌面应用</Select.Option>
                  <Select.Option value='registerPage'>注册页</Select.Option>
                  <Select.Option value='AccountSystem'>后台添加</Select.Option>
                  <Select.Option value='mindNote'>MindNote</Select.Option>
                  <Select.Option value='other'>其他</Select.Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item<IFieldType> label='来源渠道' name='source'>
                <Input placeholder='请输入' />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item<IFieldType> label='渠道推广' name='channelMarketing'>
                <Cascader
                  options={channelOptions}
                  placeholder='请选择'
                  multiple
                  changeOnSelect
                  style={{ width: '100%' }}
                />
              </Form.Item>
            </Col>
            <Col span={6} style={{ display: 'flex', justifyContent: 'end' }}>
              <Form.Item>
                <Space>
                  <Button
                    htmlType='button'
                    onClick={() => {
                      setSearchFormData({ current: 1, pageSize: 20 })
                      form.resetFields()
                    }}
                  >
                    重置
                  </Button>
                  <Button type='primary' htmlType='submit'>
                    查询
                  </Button>
                </Space>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[0, 12]} style={{ width: '100%' }}>
            <div className='flex mt-12px w-full'>
              <Col span={6}>
                <Form.Item<IFieldType>
                  label='注册页版本'
                  name='loginPageVersion'
                >
                  <Select placeholder='请选择'>
                    <Select.Option value='v1'>V1(老版本)</Select.Option>
                    <Select.Option value='v2'>V2(新版本)</Select.Option>
                  </Select>
                </Form.Item>
              </Col>
              <div className='w-full pl-12px'>
                <Row gutter={[0, 12]}>
                  <Col span={8}></Col>
                  <Col></Col>
                  <Col span={16} className='flex justify-end'>
                    <Button
                      type='primary'
                      style={{ marginRight: '16px' }}
                      onClick={exportExcelBtn}
                      loading={excelRunLoading}
                    >
                      导出
                    </Button>
                  </Col>
                </Row>
              </div>
            </div>
          </Row>
        </Form>
        <Table
          loading={listLoading}
          style={{ margin: '8px 12px' }}
          bordered
          size='middle'
          dataSource={tableData.data}
          rowKey={'id'}
          columns={columns}
          scroll={{ x: 'auto' }}
          pagination={{
            current: searchFormData.current,
            pageSize: searchFormData.pageSize,
            total: tableData.total,
            showTotal: total => <div>{`共 ${total} 条`}</div>,
            defaultPageSize: 20,
            onChange: (page, pageSize) => {
              setSearchFormData(preSearchFormData => ({
                ...preSearchFormData,
                current: page,
                pageSize,
              }))
            },
          }}
        />
      </div>
    </div>
  )
}
