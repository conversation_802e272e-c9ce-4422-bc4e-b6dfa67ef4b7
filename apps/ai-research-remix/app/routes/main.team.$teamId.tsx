import { type MetaFunction, type LoaderFunctionArgs } from '@remix-run/node'
import { useParams, useNavigate } from '@remix-run/react'
import { memo, useMemo } from 'react'
import NiceModal from '@ebay/nice-modal-react'
import { useRequest } from 'ahooks'
import { Spin, Empty, Typography } from 'antd'
import type { TeamAgent, TeamToolItem } from '@apis/mindnote/team'
import { getTeamAgentToolList } from '@apis/mindnote/team'
import { InsertTool } from '@/pages/team/InsertTool'
import { IconButton } from '@/components/base/icon'
import OptionTool from '@/pages/team/InsertTool/components/OptionTool'
import { ScrollView } from '@/components/base/scroll-view'

export const meta: MetaFunction = () => [{ title: '团队详情' }]

export async function loader({ context }: LoaderFunctionArgs) {
  return {
    user: context.user,
    token: context.userToken as string,
  }
}

const NewToolButton: React.FC<{
  agentId?: string
  flowIds?: string[]
  className?: string
  children?: React.ReactNode
  onRefresh: () => void
}> = ({
  agentId,
  flowIds,
  className = '',
  children = '添加工具',
  onRefresh,
}) => (
  <button
    onClick={e => {
      e?.stopPropagation()
      NiceModal.show(InsertTool, {
        scene: 'flow',
        agentId,
        flowIds: flowIds || [],
        onOk: () => {
          onRefresh()
        },
      })
    }}
    className={`opacity-0 group-hover:opacity-100 flex items-center gap-2 px-4 py-2 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-300 self-center ${className}`}
  >
    <span className='text-gray-700'>+</span>
    <span className='text-sm font-medium text-gray-700'>{children}</span>
  </button>
)

const AgentCard: React.FC<{
  agent: TeamAgent
  onRefresh: () => void
}> = ({ agent, onRefresh }) => {
  const tools = agent.tools || []
  return (
    <div className='group overflow-hidden mt-[34px]'>
      {/* Agent 头部 */}
      <div className='flex items-center gap-4 transition-colors min-w-0'>
        {/* Agent 头像 */}
        <div className='w-80px h-80px rounded-full flex items-center justify-center flex-shrink-0 overflow-hidden'>
          {agent.icon ? (
            <img
              src={agent.icon}
              alt={agent.agent_name}
              className='w-80px h-80px object-cover'
            />
          ) : (
            <div
              className='w-full h-full rounded-full flex items-center justify-center'
              style={{ backgroundColor: '#3B82F6' }}
            >
              <div className='w-8 h-8 rounded-full bg-white/20 flex items-center justify-center'>
                <div className='w-6 h-6 rounded-full bg-white/40' />
              </div>
            </div>
          )}
        </div>
        {/* Agent 信息 */}
        <div className='flex-1 min-w-0'>
          <h3 className="font-['PingFang_SC'] text-20px font-500 leading-18px tracking-0 mb-12px text-[#17171D]">
            {`${agent.agent_name} ${agent.agent_expert_name}`}
          </h3>
          <p className="font-['PingFang_SC'] text-14px font-normal leading-14px tracking-0 text-[#8D8D99]">
            {agent.agent_desc || ''}
          </p>
        </div>
        <NewToolButton
          agentId={agent.agent_id}
          flowIds={agent.flowIds}
          onRefresh={onRefresh}
        />
      </div>
      {/* 工具列表 */}
      <div className='mt-24px mb-50px'>
        {tools.length > 0 ? (
          <div className='grid grid-cols-3 md:grid-cols-2 xl:grid-cols-4 gap-24px'>
            {tools.map((tool: TeamToolItem) => (
              <div
                key={tool.function_id}
                className={`bg-white p-24px rounded-20px border border-[#E1E1E5 80%] hover:shadow-sm transition-shadow ${
                  tool.plugin_type === 'SYSTEM_AGENT_TOOL'
                    ? ''
                    : 'cursor-pointer'
                }`}
                onClick={() => {
                  if (tool.plugin_type === 'SYSTEM_AGENT_TOOL') return
                  NiceModal.show(OptionTool, {
                    agentId: agent.agent_id,
                    mode: 'edit',
                    flowId: tool.config?.flow_id || '',
                    name: tool.name,
                    functionId: tool.function_id,
                    currentWorkSpaceId: tool.config?.workspace_id || '',
                    applicationInfo: {
                      ...tool,
                      applicationType: 'AI',
                      id: tool.function_id,
                      flowId: tool.config?.flow_id,
                    },
                    onOk: () => {
                      onRefresh()
                    },
                  })
                }}
              >
                <div className='flex flex-col items-start'>
                  {/* 1. icon */}
                  {tool.icon?.startsWith('http') ? (
                    <img
                      src={tool.icon}
                      alt={tool.name}
                      className='w-40px h-40px object-cover mb-12px'
                    />
                  ) : (
                    <div
                      className='w-40px h-40px p-8px rounded-12px flex items-center justify-center text-lg mb-12px'
                      style={{ backgroundColor: tool.color || '#E5E7EB' }}
                    >
                      {tool.icon || '🔧'}
                    </div>
                  )}
                  {/* 2. name */}
                  <div className="font-['PingFang_SC'] text-16px font-500 leading-16px text-[#17171D] mb-7px">
                    {tool.name}
                  </div>
                  {/* 3. plugin_type */}
                  <span className="font-['PingFang_SC'] text-14px font-normal leading-14px text-[#8D8D99] mb-12px">
                    {`@${(() => {
                      switch (tool.plugin_type) {
                        case 'AI':
                          return '工作流'
                        case 'SYSTEM_AGENT_TOOL':
                          return '官方'
                        default:
                          return tool.plugin_type
                      }
                    })()}`}
                  </span>
                  {/* 4. description */}
                  <Typography.Text
                    className="font-['PingFang_SC'] text-14px font-400 leading-14px text-[#3F3F44] !mb-0"
                    ellipsis={{
                      tooltip: tool?.config?.description || tool?.description,
                    }}
                  >
                    {tool?.config?.description ||
                      tool?.description ||
                      '暂无描述'}
                  </Typography.Text>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className='h-10'></div>
        )}
      </div>
    </div>
  )
}

function TeamDetail() {
  const params = useParams()
  const navigate = useNavigate()
  const teamId = params.teamId as string

  const {
    data: teamAgentToolData,
    loading,
    error,
    refresh: refreshTeamData,
  } = useRequest(
    async () => {
      if (!teamId) throw new Error('Team ID is required')
      const response = await getTeamAgentToolList(teamId)
      return response
    },
    {
      refreshDeps: [teamId],
      onError: err => {
        console.error('获取团队Agent和工具列表失败:', err)
      },
    },
  )

  const team = teamAgentToolData
    ? {
        team_id: teamAgentToolData.team_id || teamId,
        team_name: teamAgentToolData.team_name || '未知团队',
      }
    : null

  const agentList: TeamAgent[] = useMemo(
    () =>
      (teamAgentToolData?.agents || []).map(agent => ({
        ...agent,
        flowIds:
          agent.tools?.map(tool => tool.config?.flow_id).filter(Boolean) || [],
      })),
    [teamAgentToolData],
  )

  if (loading) {
    return (
      <div className='size-full flex-center bg-white rd-24px'>
        <div className='flex-1 flex items-center justify-center'>
          <Spin size='large' />
        </div>
      </div>
    )
  }

  if (error || !team) {
    return (
      <div className='size-full flex-center bg-white rd-24px'>
        <div className='flex-1 flex items-center justify-center'>
          <div className='text-center'>
            <Empty
              description={
                <div>
                  <div className='text-xl mb-4'>
                    {error
                      ? '获取团队详情失败'
                      : `团队不存在 (TeamId: ${teamId})`}
                  </div>
                  {error && (
                    <div className='text-gray-500 mb-4'>
                      {error.message || '请检查网络连接或稍后重试'}
                    </div>
                  )}
                </div>
              }
            />
            <button
              onClick={() => navigate('/main/team')}
              className='px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600'
            >
              返回团队列表
            </button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className='size-full relative overflow-hidden bg-white rounded-[24px] flex flex-col'>
      <div
        className='absolute inset-0 rounded-[24px] z-0'
        style={{
          background:
            'linear-gradient(180deg, #FFFFFF 11%, #FFFFFF 11%, rgba(255, 255, 255, 0) 61%), linear-gradient(282deg, rgba(184, 218, 255, 0.7) 0%, rgba(216, 237, 254, 0.7) 38%, rgba(232, 251, 255, 0.7) 73%), #FFFFFF',
          filter: 'blur(360px)',
        }}
      />
      <div className='relative z-10 flex flex-col h-full'>
        {/* 顶部返回和团队名 */}
        <div
          style={{
            paddingLeft: 24,
            paddingTop: 18,
            marginBottom: 6,
            display: 'inline-flex',
          }}
          className='items-center cursor-pointer'
          onClick={() => navigate('/main/team')}
        >
          <IconButton
            icon='i-icons-arrow-left'
            size='size-32px'
            className='text-#17171D'
          />
          <span
            style={{
              fontFamily: 'PingFang SC',
              fontWeight: 500,
              fontSize: 20,
              marginLeft: 8,
            }}
          >
            {team.team_name}
          </span>
        </div>
        <ScrollView>
          <div className='flex-1 px-6 md:px-12 lg:px-16 xl:px-20 overflow-y-auto'>
            <div className='space-y-6'>
              {agentList.map(agent => (
                <AgentCard
                  key={agent.agent_id}
                  agent={agent}
                  onRefresh={refreshTeamData}
                />
              ))}
            </div>
            {agentList.length === 0 && <div className='h-10'></div>}
          </div>
        </ScrollView>
      </div>
    </div>
  )
}

export default !import.meta.env.SSR ? memo(TeamDetail) : () => null
