import { json } from '@remix-run/node'
import type { MetaFunction, LoaderFunctionArgs } from '@remix-run/node'
import { useParams } from '@remix-run/react'
import { useTitle } from 'ahooks'
import NextAgentPage from '@/pages/next-agent/page'
import { memo } from 'react'

export const meta: MetaFunction = () => {
  return [{ title: 'Next Agent' }]
}

export async function loader({ context }: LoaderFunctionArgs) {
  return json({
    user: context.user,
    token: context.userToken as string,
  })
}

function NextAgent() {
  return <NextAgentPage isShareMode={true} />
}

export default !import.meta.env.SSR ? memo(NextAgent) : () => null
