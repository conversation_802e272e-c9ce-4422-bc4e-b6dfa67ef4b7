// import { IconFont } from '@bty/components'
import type { FC, ReactNode } from 'react'
import { useMemo } from 'react'
import { cn } from '@bty/util'
import { EventType, EventStatus } from '@apis/mindnote/next-agent-chat.type'
import { Icon } from '../../../../components/base/icon'
import {
  useEventSnapshot,
  useLatestTaskEvent,
  type MessageRenderProps,
} from '@/store/task-event'
import { Tabs, useTaskStore } from '@/store/task'

export interface ActionProps {
  icon: string
  name?: string
  className?: string
  arguments?: string[]
  base: MessageRenderProps['base']
}

export type IconType =
  | 'prepare'
  | 'plan_analyze'
  | 'file_operator'
  | 'mcp'
  | 'update'
  | 'browser_use'
  | 'tool_call'
  | 'code_execute'
  | 'web_fetch'
  | 'create_file'
  | 'str_replace'
  | 'terminal_operator'
  | 'read_file'
  | 'web_search'

// 动画样式常量
const ANIMATION_STYLES = {
  duration: '2.5s',
  easing: 'ease-in-out',
  iteration: 'infinite',
} as const

// 检查事件是否完成
function isEventCompleted(status?: EventStatus): boolean {
  return (
    status === EventStatus.SUCCESS ||
    status === EventStatus.FAILED ||
    status === EventStatus.COMPLETED
  )
}

// 根据图标类型返回对应的图标组件
function getIconComponent(iconType: IconType): ReactNode {
  const iconMap: Record<IconType, string> = {
    prepare: 'i-icons-next-agent-prepare',
    tool_call: 'i-icons-next-agent-prepare',
    str_replace: 'i-icons-next-agent-update',
    update: 'i-icons-next-agent-update',
    read_file: 'i-icons-next-agent-refer',
    create_file: 'i-icons-next-agent-file',
    file_operator: 'i-icons-next-agent-file',
    plan_analyze: 'i-icons-next-agent-plan_analyze',
    mcp: 'i-icons-next-agent-mcp',
    browser_use: 'i-icons-search-network',
    code_execute: 'i-icons-next-agent-code',
    terminal_operator: 'i-icons-next-agent-code',
    web_fetch: 'i-icons-next-agent-analysis',
    web_search: 'i-icons-next-agent-analysis',
  }

  const iconName = iconMap[iconType] || 'i-icons-search-network'

  return (
    <Icon
      icon={iconName}
      size='size-16px'
      className='text-#8D8D99 mr-4px shrink-0'
    />
  )
}

/**
 * 进行中状态的动画组件 - 斜着的光条从左到右扫描效果
 */
const RunningIndicator: FC = () => {
  const animationProps = useMemo(
    () => ({
      transform: 'translateX(-100%)',
      animation: `sweepSlash ${ANIMATION_STYLES.duration} ${ANIMATION_STYLES.easing} ${ANIMATION_STYLES.iteration}`,
    }),
    [],
  )

  return (
    <div className='absolute inset-0 rounded-10px overflow-hidden'>
      {/* 主光条 */}
      <div
        className='absolute inset-0'
        style={{
          background:
            'linear-gradient(120deg, transparent 40%, rgba(255,255,255,0.9) 50%, transparent 60%)',
          boxShadow: '0 0 20px rgba(255,255,255,0.5)',
          ...animationProps,
        }}
      />
      {/* 扩散光晕 */}
      <div
        className='absolute inset-0'
        style={{
          background:
            'linear-gradient(120deg, transparent 35%, rgba(255,255,255,0.3) 50%, transparent 65%)',
          filter: 'blur(3px)',
          ...animationProps,
        }}
      />
      <style
        dangerouslySetInnerHTML={{
          __html: `
            @keyframes sweepSlash {
              0% { transform: translateX(-100%); }
              100% { transform: translateX(100%); }
            }
          `,
        }}
      />
    </div>
  )
}

const Action: FC<ActionProps> = props => {
  const { icon, name, className = '', arguments: args, base } = props

  const { setSnapshot } = useEventSnapshot()
  const showPanel = useTaskStore(state => state.showPanel)
  const setActiveTab = useTaskStore(state => state.setActiveTab)
  const latestEvent = useLatestTaskEvent()

  // 检查是否为最新事件
  const isLatest = useMemo(
    () => base?.event_id === latestEvent?.event_id,
    [base?.event_id, latestEvent?.event_id],
  )

  // 检查是否为运行中状态 TODO: 等待后端逻辑处理正确后 改为 !isEventCompleted(base?.event_status) || isLatest,
  const isRunning = useMemo(
    () => !isEventCompleted(base?.event_status) && isLatest,
    [base?.event_status, isLatest],
  )

  const iconComponent = useMemo(
    () => getIconComponent(icon as IconType),
    [icon],
  )

  const handleClick = () => {
    if (base?.event_type === EventType.PLAN_ANALYZE) {
      return
    }
    setSnapshot(base?.event_id || undefined)
    setActiveTab(Tabs.Computer)
    showPanel()
  }

  if (!name || !icon) {
    return null
  }

  return (
    <div
      onClick={handleClick}
      className={cn(
        'cursor-pointer flex items-center px-6px py-4px my-4px rd-10px text-14px/22px bg-[rgba(228,228,228,0.5)] max-w-full overflow-hidden c-#3F3F44 !hover:bg-[rgba(228,228,228,1)] relative transition-all duration-800 ease-in-out',
        className,
      )}
    >
      {/* 运行中状态的动画背景 */}
      {isRunning && <RunningIndicator />}

      {/* 内容部分 */}
      <div className='relative z-1 flex items-center w-full'>
        {iconComponent}
        {name && (
          <div className='shrink-0 text-14px/22px c-#3F3F44'>{name}</div>
        )}
        {args && (
          <div className='max-w-560px ml-4px c-#8D8D99 truncate'>
            {args?.join(' ')}
          </div>
        )}
      </div>
    </div>
  )
}

export default Action
