import { useKey<PERSON>ress } from 'ahooks'
import { useCallback, useEffect, useMemo, useState } from 'react'
import classNames from 'classnames'
import { Tooltip } from 'antd'
import { updateConversationPublicStatus } from '@apis/mindnote/next-agent-chat'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import { IconButton } from '@/components/base/icon'
import { Tabs, useTaskPanel } from '@/store/task'
import { BLANK_CONVERSATION_ID, useTaskListStore } from '@/store/task-list'
import { useEventSnapshot } from '@/store/task-event'
import { useNextAgent } from './provider/NextAgentProvider'
import {
  handleShareNextAgentUrl,
  ShareTaskPopover,
} from './components/ShareTaskPopover'

export function ChatHeader() {
  const {
    conversationIsPublic,
    setConversationPublicStatus,
    isShareMode,
    taskTitle,
    taskStatus,
  } = useNextAgent()

  const taskIsCompleted = [
    TaskStatus.COMPLETED,
    TaskStatus.FAILED,
    TaskStatus.CANCELED,
  ].includes(taskStatus)

  const { allSnapshots } = useEventSnapshot()

  const showList = useTaskListStore(s => s.show)
  const setShowList = useTaskListStore(s => s.setShow)
  const taskId = useTaskListStore(s => s.taskId)
  const setTaskId = useTaskListStore(s => s.setTaskId)
  const taskList = useTaskListStore(s => s.getTaskList())
  const [sharePopoverOpen, setSharePopoverOpen] = useState(false)

  const [teamName, setTeamName] = useState('')

  const { hidePanel, showPanel, activeTab, setActiveTab } = useTaskPanel()

  const handleAdd = useCallback(() => {
    setTaskId()
    hidePanel()
  }, [])

  useKeyPress('meta.k', handleAdd)

  const handleHistoryListCollapse = useCallback(() => {
    // 打开任务列表的时候隐藏详情面板
    if (!showList) {
      hidePanel()
    }
    setShowList(!showList)
  }, [showList])

  const title = useMemo(() => {
    if (isShareMode) {
      return taskTitle || teamName || 'Next Agent'
    }
    const now = taskList.find(e => e.conversation_id === taskId)
    if (!now) {
      return teamName || 'Next Agent'
    }
    const group = useTaskListStore.getState().group
    const groupInfo = useTaskListStore.getState().map[group]
    return group === 'daily' ? now.title : `${groupInfo?.name} ${now.title}`
  }, [teamName, taskId, taskList, taskTitle, isShareMode])

  const handleUpdateConversationPublicStatus = useCallback(
    async (status: boolean) => {
      if (!taskId) return
      await updateConversationPublicStatus(taskId, status)
      setConversationPublicStatus(status)
    },
    [taskId, setConversationPublicStatus],
  )

  useEffect(() => {
    const handle = (e: any) => {
      setTeamName(e.detail?.team_name || '')
    }
    document.addEventListener('team-change', handle)
    return () => {
      document.removeEventListener('team-change', handle)
    }
  }, [])

  const canShare = taskId && taskId !== BLANK_CONVERSATION_ID && taskIsCompleted

  const leftActions = [
    { icon: 'i-icons-list', onClick: handleHistoryListCollapse },
    { icon: 'i-icons-add', onClick: handleAdd },
  ]

  const showSandboxEntry = !!allSnapshots?.length && activeTab !== Tabs.Computer

  const showArtifactEntry =
    !!allSnapshots?.length && activeTab !== Tabs.Artifact

  return (
    <header className='h-60px flex relative shrink-0'>
      {!isShareMode && !showList && (
        <div className='h-36px flex-center absolute left-16px top-16px'>
          {leftActions.map(({ icon, onClick }) => (
            <IconButton
              key={icon}
              icon={icon}
              iconSize='size-18px'
              size='size-36px'
              className='text-font mr-12px'
              onClick={onClick}
            />
          ))}
        </div>
      )}

      <div className='flex-1 flex flex-center gap-8px pt-16px'>
        <div className='font-500 text-16px ml-24px'>{title}</div>
        <div className={classNames('absolute right-24px mt-[-3px] flex')}>
          {canShare ? (
            !isShareMode ? (
              <ShareTaskPopover
                isShareMode={isShareMode}
                open={sharePopoverOpen}
                onOpenChange={setSharePopoverOpen}
                status={conversationIsPublic}
                onValueChange={handleUpdateConversationPublicStatus}
                taskId={taskId}
              >
                <Tooltip title='分享当前会话'>
                  <IconButton
                    icon='i-icons-share'
                    iconSize='size-16px'
                    size='size-32px'
                    className='text-font ml-8px hover:bg-#14626999/8 rounded-8px'
                  />
                </Tooltip>
              </ShareTaskPopover>
            ) : (
              <Tooltip title='分享当前会话'>
                <IconButton
                  onClick={() => {
                    handleShareNextAgentUrl(taskId!)
                  }}
                  icon='i-icons-share'
                  iconSize='size-16px'
                  size='size-32px'
                  className='text-font ml-8px hover:bg-#14626999/8 rounded-8px'
                />
              </Tooltip>
            )
          ) : null}

          {showSandboxEntry && (
            <IconButton
              className='ml-8px'
              size='size-32px'
              icon='i-icons-collapse rotate-180'
              iconSize='size-16px'
              onClick={() => {
                setActiveTab(Tabs.Computer)
                showPanel()
              }}
            />
          )}

          {showArtifactEntry && (
            <IconButton
              className='ml-8px'
              size='size-32px'
              icon='i-icons-artifacts'
              iconSize='size-16px'
              onClick={() => {
                setActiveTab(Tabs.Artifact)
                showPanel()
              }}
            />
          )}
        </div>
      </div>
    </header>
  )
}
