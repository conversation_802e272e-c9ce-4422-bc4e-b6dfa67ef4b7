import type { ReactNode, HTMLAttributes } from 'react'

interface CardBaseProps
  extends Omit<HTMLAttributes<HTMLDivElement>, 'children'> {
  children: ReactNode
  borderRadius?: string
  hoverBorderRadius?: string
  shadow?: string
  hoverShadow?: string
  contentInset?: string
  disableHover?: boolean
  transitionDuration?: string
  backgroundColor?: string
  border?: string
  className?: string
}

// 简单的类名合并工具
function cn(...classes: (string | undefined | false | null)[]): string {
  return classes.filter(Boolean).join(' ')
}

/**
 * 通用卡片底片组件
 * 提供灵活的样式配置，支持自定义圆角、阴影、内边距等
 */
export function CardBase({
  children,
  borderRadius = 'rounded-xl',
  hoverBorderRadius,
  shadow = 'shadow-sm',
  hoverShadow = 'hover:shadow-lg',
  contentInset = 'p-4',
  disableHover = false,
  transitionDuration = 'transition-all duration-200',
  backgroundColor = 'bg-white',
  border = 'border border-gray-200',
  className,
  ...rest
}: CardBaseProps) {
  // 组合卡片外层样式
  const cardClasses = cn(
    backgroundColor,
    border,
    borderRadius,
    shadow,
    transitionDuration,
    !disableHover && hoverShadow,
    !disableHover && hoverBorderRadius,
    !disableHover && 'cursor-pointer',
    className,
  )

  // 内容区域样式
  const contentClasses = cn(contentInset, 'w-full h-full')

  return (
    <div className={cardClasses} {...rest}>
      <div className={contentClasses}>{children}</div>
    </div>
  )
}

// 预设样式，提供常用的卡片样式配置
export const CardBasePresets = {
  default: {
    borderRadius: 'rounded-xl',
    shadow: 'shadow-sm',
    hoverShadow: 'hover:shadow-lg',
    contentInset: 'p-4',
  },
  compact: {
    borderRadius: 'rounded-lg',
    shadow: 'shadow-sm',
    hoverShadow: 'hover:shadow-md',
    contentInset: 'p-3',
  },
  large: {
    borderRadius: 'rounded-2xl',
    shadow: 'shadow-md',
    hoverShadow: 'hover:shadow-xl',
    contentInset: 'p-6',
  },
  flat: {
    borderRadius: 'rounded-lg',
    shadow: 'shadow-none',
    hoverShadow: 'hover:shadow-sm',
    contentInset: 'p-4',
    border: 'border-2 border-gray-100',
  },
  floating: {
    borderRadius: 'rounded-2xl',
    shadow: 'shadow-lg',
    hoverShadow: 'hover:shadow-2xl',
    contentInset: 'p-5',
    border: 'border-0',
  },
}
