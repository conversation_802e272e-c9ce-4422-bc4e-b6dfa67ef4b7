import { create } from 'zustand'

export const Tabs = {
  Computer: 'computer',
  Artifact: 'artifact',
  Plan: 'plan',
} as const

type TabType = (typeof Tabs)[keyof typeof Tabs]

interface TaskStore {
  panelVisible: boolean
  showPanel: VoidFunction
  hidePanel: VoidFunction
  togglePanel: VoidFunction
  activeTab: TabType | null
  setActiveTab: (activeTab: TabType) => void
  previousTab: typeof Tabs.Computer | null
}

const initialState = {
  panelVisible: false,
  activeTab: null,
  previousTab: null,
}

export const useTaskStore = create<TaskStore>(set => ({
  ...initialState,

  showPanel: () => set({ panelVisible: true }),

  hidePanel: () => set({ panelVisible: false, activeTab: null }),

  togglePanel: () =>
    set(state => {
      // 如果当前是打开状态，即将关闭，则执行回退逻辑
      if (state.panelVisible) {
        // 如果需要回退且有前一个 tab，则回退到前一个 tab
        if (state.previousTab) {
          return {
            panelVisible: true,
            activeTab: state.previousTab,
            previousTab: null,
          }
        }
        // 否则正常关闭 panel
        return {
          panelVisible: false,
          activeTab: null,
        }
      }
      // 如果当前是关闭状态，则正常打开
      return { panelVisible: true }
    }),

  setActiveTab: activeTab =>
    set(state => {
      // 特殊情况：从 computer 切换到 artifact 时，记录需要回退
      const shouldReturn =
        state.activeTab === Tabs.Computer && activeTab === Tabs.Artifact

      return {
        activeTab,
        previousTab: shouldReturn ? Tabs.Computer : null,
      }
    }),
}))

export function useTaskPanel() {
  const panelVisible = useTaskStore(state => state.panelVisible)
  const showPanel = useTaskStore(state => state.showPanel)
  const hidePanel = useTaskStore(state => state.hidePanel)
  const togglePanel = useTaskStore(state => state.togglePanel)

  const activeTab = useTaskStore(state => state.activeTab)
  const setActiveTab = useTaskStore(state => state.setActiveTab)

  return {
    panelVisible,
    showPanel,
    hidePanel,
    togglePanel,
    activeTab,
    setActiveTab,
  }
}
