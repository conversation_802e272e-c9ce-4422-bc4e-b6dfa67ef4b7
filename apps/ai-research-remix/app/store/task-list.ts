import { create } from 'zustand'
import {
  createNextAgentConversation,
  deleteNextAgentConversation,
  getConversationInfo,
  getNextAgentConversationList,
  pollConversationIds,
  updateNextAgentConversation,
} from '@apis/mindnote/next-agent-chat'
import { getScheduleDetail, getScheduleList } from '@apis/mindnote/schedule'
import { TaskStatus } from '@apis/mindnote/next-agent-chat.type'
import type { TaskListItem } from '@apis/mindnote/next-agent-chat.type'
import dayjs from 'dayjs'
import { wait } from '@/util/date'

class GroupedList {
  name = ''
  id = ''
  avatar = ''
  unReadCount = 0
  enabled = true
  open = false
  loading = true
  pageNo = 0
  pageSize = 20
  hasMore = false
  total = 0
  items: TaskListItem[] = []

  constructor(name = '', id = '') {
    this.name = name
    this.id = id
  }
}

export const BLANK_CONVERSATION_ID = 'BLANK_CONVERSATION_ID'

const NEW_TASK = '新任务'
const COMPLETED_STATUSES: TaskStatus[] = [
  TaskStatus.COMPLETED,
  TaskStatus.FAILED,
  TaskStatus.CANCELED,
]

let isInit = false
let initTaskId = BLANK_CONVERSATION_ID
const pollingTimer: Record<string, any> = {}

if (
  !import.meta.env.SSR &&
  (window.location.pathname === '/main/next-agent' ||
    window.location.pathname === '/share/next-agent')
) {
  initTaskId =
    new URL(window.location.href)?.searchParams?.get('id') ||
    BLANK_CONVERSATION_ID
}

function checkTaskState(list: TaskListItem[]) {
  const hasIncomplete = list.some(task => {
    const taskState = task.task_state || (task as any).status
    return taskState && !COMPLETED_STATUSES.includes(taskState)
  })
  return hasIncomplete
}

interface Store {
  show: boolean
  setShow: (show: boolean) => void

  tab: 'daily' | 'schedule'
  setTab: (tab: 'daily' | 'schedule') => void

  schedulePage: {
    pageNo: number
    pageSize: number
    loading: boolean
    hasMore: boolean
  }
  scheduleList: string[]
  map: Record<string, GroupedList>
  init: () => Promise<void>
  reset: () => void
  updateDailyTask: (reload?: boolean) => Promise<GroupedList>
  updateScheduleList: (reload?: boolean) => Promise<void>
  updateScheduleTask: (
    groupId: string,
    reload?: boolean,
  ) => Promise<GroupedList>

  group: string
  setGroup: (id: string) => void
  openGroup: (id: string, state?: boolean) => void

  taskId: string
  setTaskId: (id?: string) => void
  getTaskList: (group?: string) => TaskListItem[]

  createTask: (
    teamId: string,
    group?: string,
  ) => Promise<{
    conversation_id: string
    team_id: string
  }>
  deleteTask: (id: string, group?: string) => Promise<void>
  updateTask: (
    id: string,
    data: Partial<TaskListItem>,
    group?: string,
  ) => Promise<void>

  startPolling: (groupId: string) => void
  stopPolling: (groupId: string) => void
  clearPolling: () => void
}

export const useTaskListStore = /* @__PURE__ */ create<Store>((set, get) => ({
  show: true,
  setShow: (show: boolean) => set({ show }),

  tab: 'daily',
  setTab: (tab: 'daily' | 'schedule') => {
    set({ tab })
    if (tab === 'daily') {
      get().startPolling('daily')
      get().scheduleList.forEach(id => {
        get().stopPolling(id)
      })
    } else {
      get().scheduleList.forEach(id => {
        get().startPolling(id)
      })
      get().stopPolling('daily')
    }
  },

  schedulePage: { pageNo: 0, pageSize: 20, loading: true, hasMore: false },
  scheduleList: [],
  map: {},
  init: async () => {
    if (!isInit) {
      isInit = true
      const dailyGroup = new GroupedList('daily', 'daily')
      dailyGroup.open = true
      set({ map: { daily: dailyGroup } })
      await get().updateScheduleList(true)
      if (initTaskId && initTaskId !== BLANK_CONVERSATION_ID) {
        const res = await getConversationInfo(initTaskId)
        if (res.definition_id) {
          get().setTab('schedule')
          get().setGroup(res.definition_id)
          get().openGroup(res.definition_id, true)
        }
      }
      await get().updateDailyTask(true)
      get().startPolling('daily')
    } else {
      const tab = get().tab
      if (tab === 'daily') {
        get().startPolling('daily')
      } else {
        get().scheduleList.forEach(id => {
          get().startPolling(id)
        })
      }
    }
  },
  reset: () => {
    isInit = false
    set({
      show: true,
      tab: 'daily',
      schedulePage: { pageNo: 0, pageSize: 20, loading: true, hasMore: false },
      scheduleList: [],
      map: {},
    })
    get().clearPolling()
  },

  updateDailyTask: async (reload = false) => {
    const grouped = get().map.daily!
    let no = (grouped.pageNo || 0) + 1
    let size = grouped.pageSize || 20
    if (reload && grouped.pageNo > 0) {
      no = 1
      size = grouped.pageNo * grouped.pageSize
    }
    set({ map: { ...get().map, daily: { ...grouped, loading: true } } })
    try {
      const dailyList = await getNextAgentConversationList(no, size)
      const temp = { ...get().map.daily! }
      if (!reload) {
        temp.pageNo = no
        temp.items = [...temp.items, ...dailyList.data]
      } else {
        temp.pageNo = temp.pageNo === 0 ? 1 : temp.pageNo
        temp.items = dailyList.data
      }
      temp.loading = false
      temp.hasMore = temp.items.length < dailyList.total
      temp.total = dailyList.total || 0
      set({ map: { ...get().map, daily: { ...temp } } })
      return temp
    } catch (error) {
      set({ map: { ...get().map, daily: { ...grouped, loading: false } } })
      throw error
    }
  },
  updateScheduleList: async (reload = false) => {
    const page = get().schedulePage
    let no = page.pageNo + 1
    const size = page.pageSize
    if (reload && page.pageNo > 0) {
      no = 1
    }
    set({ schedulePage: { ...page, loading: true } })
    const list = await getScheduleList(no, size)
    const scheduleMap: Record<string, GroupedList> = {}

    const map = get().map
    for (const item of list.data) {
      const scheduleGroup =
        map[item.definition_id] ??
        new GroupedList(item.name, item.definition_id)

      scheduleGroup.name = item.name
      scheduleGroup.avatar = item.execution_team_icon
      scheduleGroup.unReadCount = item.unread_count || 0
      scheduleGroup.enabled = item.is_enabled ?? true

      if (map[item.definition_id]) {
        continue
      }

      scheduleGroup.open = false
      scheduleMap[scheduleGroup.id] = scheduleGroup
    }

    set({
      schedulePage: {
        pageNo: no,
        pageSize: size,
        loading: false,
        hasMore: list.data.length < list.total,
      },
      scheduleList: list.data.map((item: any) => item.definition_id),
      map: { ...get().map, ...scheduleMap },
    })
  },
  updateScheduleTask: async (groupId: string, reload = false) => {
    const grouped = get().map[groupId]!
    let no = (grouped.pageNo || 0) + 1
    let size = grouped.pageSize || 20
    if (reload && grouped.pageNo > 0) {
      no = 1
      size = grouped.pageNo * grouped.pageSize
    }
    set({ map: { ...get().map, [groupId]: { ...grouped, loading: true } } })
    try {
      const scheduleTaskList = await getScheduleDetail(groupId, no, size)
      const temp = { ...get().map[groupId]! }
      const newTaskList = scheduleTaskList.data.map((task: any) => {
        const create = dayjs(task.created_at)
        const diff = create.diff(dayjs(), 'year')
        return {
          ...task,
          title: create.format(
            diff > 1 ? 'YYYY-MM-DD MM-DD HH:mm' : 'MM-DD HH:mm',
          ),
        }
      })
      console.log(newTaskList)

      if (!reload) {
        temp.pageNo = no
        temp.items = [...temp.items, ...newTaskList]
      } else {
        temp.pageNo = temp.pageNo === 0 ? 1 : temp.pageNo
        temp.items = newTaskList
      }
      temp.loading = false
      temp.hasMore = temp.items.length < scheduleTaskList.total
      temp.total = scheduleTaskList.total || 0
      set({ map: { ...get().map, [groupId]: { ...temp } } })
      return temp
    } catch (error) {
      set({ map: { ...get().map, [groupId]: { ...grouped, loading: false } } })
      throw error
    }
  },

  group: 'daily',
  setGroup: (id: string) => {
    set({ group: id })
  },
  openGroup: async (groupId: string, state?: boolean) => {
    const grouped = get().map[groupId]
    if (!grouped) return

    grouped.open = state !== undefined ? state : !grouped.open
    set({ map: { ...get().map, [groupId]: { ...grouped } } })

    if (groupId !== 'daily' && grouped.open && !grouped.items.length) {
      await wait(300)
      await get().updateScheduleTask(groupId, true)
      get().startPolling(groupId)
    }

    if (!grouped.open) {
      get().stopPolling(groupId)
    }
  },

  taskId: initTaskId,
  setTaskId: (id?: string) => {
    if (id && id !== BLANK_CONVERSATION_ID) {
      const url = new URL(window.location.href)
      url.searchParams.set('id', id)
      window.history.replaceState({}, '', url.toString())
      set({ taskId: id })
    } else {
      const url = new URL(window.location.href)
      url.searchParams.delete('id')
      window.history.replaceState({}, '', url.toString())
      set({ taskId: BLANK_CONVERSATION_ID, group: '' })
    }
  },
  getTaskList: (group?: string) => {
    const sureGroup = group || get().group
    const grouped = get().map[sureGroup]
    if (!grouped) return []
    return grouped.items
  },

  createTask: async (teamId: string) => {
    const type = get().tab === 'daily' ? 'daily' : 'schedule'
    const res = await createNextAgentConversation(
      NEW_TASK,
      type === 'schedule' ? 'AUTOMATION' : undefined,
      teamId,
    )
    get().setTaskId(res.conversation_id)
    if (type === 'daily') {
      await get().updateDailyTask(true)
      get().startPolling('daily')
    } else {
      await get().updateScheduleList(true)
    }
    const info = await getConversationInfo(res.conversation_id)
    if (info.definition_id) {
      get().setTab('schedule')
      get().setGroup(info.definition_id)
      get().openGroup(info.definition_id, true)
    } else {
      get().setTab('daily')
      get().setGroup('daily')
    }
    return {
      conversation_id: res.conversation_id,
      team_id: teamId,
    }
  },
  deleteTask: async (id: string, group?: string) => {
    await deleteNextAgentConversation(id)

    const sureGroup = group || get().group
    const grouped = get().map[sureGroup]
    if (!grouped) return
    grouped.items = grouped.items.filter(item => item.conversation_id !== id)
    let newTaskId = get().taskId
    if (newTaskId === id) {
      newTaskId = BLANK_CONVERSATION_ID
    }
    set({
      map: { ...get().map, [sureGroup]: { ...grouped } },
      taskId: newTaskId,
    })
  },
  updateTask: async (
    id: string,
    data: Partial<TaskListItem>,
    group?: string,
  ) => {
    if (data.title) {
      await updateNextAgentConversation(id, data)
    }

    const sureGroup = group || get().group
    const grouped = get().map[sureGroup]
    grouped.items = grouped.items.map(item => {
      if (item.conversation_id === id) {
        return {
          ...item,
          ...data,
        }
      }
      return item
    })
    set({ map: { ...get().map, [sureGroup]: { ...grouped } } })
  },

  startPolling: (groupId: string) => {
    const grouped = get().map[groupId]
    const stop = get().stopPolling
    stop(groupId)

    if (
      pollingTimer[groupId] ||
      !grouped.open ||
      !checkTaskState(grouped.items)
    ) {
      return
    }

    pollingTimer[groupId] = setInterval(async () => {
      const grouped = get().map[groupId]

      const incompleteTask = grouped.items.filter(task => {
        const taskState = task.task_state
        return taskState && !COMPLETED_STATUSES.includes(taskState)
      })

      if (incompleteTask.length === 0) {
        stop(groupId)
      }

      const taskIds = incompleteTask.map(e => e.conversation_id).slice(0, 20) // 只拿前20个，保证性能

      try {
        const updateList = await pollConversationIds(taskIds)
        if (updateList?.length) {
          grouped.items = grouped.items.map(task => {
            const updatedStatus = updateList.find(
              (item: any) => item.conversation_id === task.conversation_id,
            )
            if (updatedStatus) {
              return {
                ...task,
                title: groupId === 'daily' ? updatedStatus.title : task.title,
                task_state: updatedStatus.task_state,
                status: updatedStatus.status,
              }
            }
            return task
          })
          set({ map: { ...get().map, [groupId]: { ...grouped } } })
        }
      } catch (error) {
        console.error('轮询任务状态失败:', error)
        stop(groupId)
      }
    }, 10000)
  },
  stopPolling: (groupId: string) => {
    clearInterval(pollingTimer[groupId])
    delete pollingTimer[groupId]
  },
  clearPolling: () => {
    Object.keys(pollingTimer).forEach(id => {
      get().stopPolling(id)
    })
  },
}))
