import './styles/global.css'
import 'virtual:uno.css'

import { useNavigate } from '@remix-run/react'
import { memo, useEffect } from 'react'
import { ConfigProvider, App as AntApp } from 'antd'
import zhCN from 'antd/locale/zh_CN'
import 'dayjs/locale/zh-cn'
import NiceModal from '@ebay/nice-modal-react'
import { ClickToComponent } from 'click-to-react-component'
import { ANTD_COMPONENT, ANTD_TOKEN } from './theme'

const wave = { disabled: true }
const theme = {
  token: ANTD_TOKEN,
  components: ANTD_COMPONENT,
  hashed: false,
  cssVar: true,
}

interface ClientRootProps {
  children: React.ReactNode
}

export const ClientRoot = memo(({ children }: ClientRootProps) => {
  const navigate = useNavigate()

  useEffect(() => {
    if (window.MindNote?.platform === 'macOS') {
      window.addEventListener('message', event => {
        if (event.data.action === 'showCurrentCollect') {
          const id = event.data.id
          navigate(`/main/knowledge/collection?id=${id}`)
        }
      })
    }
  }, [])

  useEffect(() => {
    if (import.meta.env.SSR) return

    const showMcpSettingListener = (e: any) => {
      if (e?.data?.action === 'mcp.show.setting.modal') {
        navigate('/main/mcp')
      }
    }

    window.addEventListener('message', showMcpSettingListener)
    return () => {
      window.removeEventListener('message', showMcpSettingListener)
    }
  }, [])

  return (
    <ConfigProvider locale={zhCN} wave={wave} theme={theme}>
      {import.meta.env.MODE === 'development' && (
        <ClickToComponent editor='cursor' />
      )}
      <NiceModal.Provider>
        <AntApp className='size-full'>{children}</AntApp>
      </NiceModal.Provider>
    </ConfigProvider>
  )
})
