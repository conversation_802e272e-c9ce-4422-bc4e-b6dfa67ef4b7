import {
  useLayoutEffect,
  useRef,
  useState,
  useCallback,
  type RefObject,
} from 'react'

interface SlideUpConfig {
  delay?: number
  duration?: number
  distance?: number
  threshold?: number
  triggerOnce?: boolean
  maxDelay?: number
}

interface AnimationState {
  isTriggered: boolean
  isAnimating: boolean
}

type SlideUpReturn<T extends HTMLElement> = [
  ref: RefObject<T>,
  state: AnimationState,
]

const DEFAULT_CONFIG: Required<SlideUpConfig> = {
  delay: 0,
  duration: 600,
  distance: 50,
  threshold: 0.1,
  triggerOnce: true,
  maxDelay: 200,
} as const

export function useSlideUp<T extends HTMLElement>(
  config: SlideUpConfig = {},
): SlideUpReturn<T> {
  const ref = useRef<T>(null)
  const [state, setState] = useState<AnimationState>({
    isTriggered: false,
    isAnimating: false,
  })

  const finalConfig = { ...DEFAULT_CONFIG, ...config }
  const { delay, duration, threshold, triggerOnce, maxDelay } = finalConfig

  const setInitialStyles = useCallback((element: HTMLElement) => {
    Object.assign(element.style, {
      transform: 'scale(0.8)',
      opacity: '0',
      transition: 'none',
      willChange: 'transform, opacity',
    })
    // eslint-disable-next-line no-unused-expressions
    element.offsetHeight
  }, [])

  const createAnimation = useCallback(
    (element: HTMLElement) => {
      const actualDelay = Math.min(delay, maxDelay)

      const animate = () => {
        setState(prev => ({ ...prev, isAnimating: true }))

        element.style.transition = `transform ${duration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94), opacity ${duration}ms cubic-bezier(0.25, 0.46, 0.45, 0.94)`

        requestAnimationFrame(() => {
          Object.assign(element.style, {
            transform: 'scale(1)',
            opacity: '1',
          })
        })

        // Cleanup after animation
        setTimeout(() => {
          element.style.willChange = 'auto'
          setState(prev => ({ ...prev, isAnimating: false }))
        }, duration)
      }

      return actualDelay > 0 ? setTimeout(animate, actualDelay) : animate()
    },
    [delay, duration, maxDelay],
  )

  const handleIntersection = useCallback(
    (entries: IntersectionObserverEntry[], observer: IntersectionObserver) => {
      entries.forEach(entry => {
        const element = entry.target as HTMLElement

        if (entry.isIntersecting) {
          setState(prev => {
            // 只有未触发过或允许重复触发时才执行动画
            createAnimation(element)

            if (triggerOnce) {
              observer.unobserve(element)
            }

            return { ...prev }
          })
        } else if (!triggerOnce) {
          // Reset for re-trigger
          setState({ isTriggered: false, isAnimating: false })
          Object.assign(element.style, {
            transition: 'none',
            transform: 'scale(0.8)',
            opacity: '0',
          })
        }
      })
    },
    [createAnimation, triggerOnce],
  )

  useLayoutEffect(() => {
    const element = ref.current
    if (!element) return

    setInitialStyles(element)

    const observer = new IntersectionObserver(
      entries => handleIntersection(entries, observer),
      {
        threshold,
        rootMargin: '0px 0px -50px 0px',
      },
    )

    observer.observe(element)

    return () => observer.disconnect()
  }, [setInitialStyles, handleIntersection, threshold])

  return [ref, state]
}
