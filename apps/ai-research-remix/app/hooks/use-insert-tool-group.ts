import { useMemoizedFn, useRequest } from 'ahooks'
import { getGroupList } from '@apis/application'
import { cloneDeep } from 'lodash-es'
import { localize } from '@bty/localize'
import type { GroupType } from '@apis/application/type'
import { useAuth } from '@bty/react-auth'
import { RoleCode } from '@apis/authority/type'
import { useEffect, useState } from 'react'
import type { AppResourceType } from '@apis/acl/type'
import useResourceAcl from './use-resource-acl'

const noGroupId = 'noGroup'

/** 分类 */
export default function useInsertToolGroup<T = any>({
  applicationType,
  currentWorkspaceId,
  appLabel,
  groupIdKey,
  originList,
}: {
  applicationType: AppResourceType
  currentWorkspaceId: string
  appLabel?: 'FLOW_GROUP'
  groupIdKey: 'appGroupId'
  originList: T[]
}) {
  const { state } = useAuth()
  const [toolList, setToolList] = useState<GroupType[]>([])
  const appResourceAcl = useResourceAcl({
    appResourceType: 'AGENT',
    workspaceId: currentWorkspaceId,
  })
  const { data: groupList } = useRequest(
    () => {
      if (currentWorkspaceId) {
        return getGroupList({
          applicationType,
          workspaceId: currentWorkspaceId,
          all_app: false,
          app_label: appLabel,
        })
      }

      return Promise.resolve([])
    },
    { refreshDeps: [currentWorkspaceId, appLabel] },
  )

  // 直接在 hook 中实现权限检查逻辑
  const checkGroupPermission = useMemoizedFn((groupId: string) => {
    // 如果是空间管理员拥有完整权限
    if (state.role === RoleCode.ADMINISTRATOR) {
      return true
    }

    if (appResourceAcl?.is_auth_all_app) {
      return true
    }

    const aclForType = appResourceAcl?.is_auth_app_by_type?.find(
      el => el.app_type === applicationType,
    )

    // 如果对应的 type 有编辑权限，直接返回 true
    if (
      aclForType?.permission_points.includes('EDIT') ||
      aclForType?.permission_points.includes('QUERY')
    ) {
      return true
    }

    const acl = appResourceAcl?.[applicationType]
    const access = acl?.groups_permission.find(el => el.group_id === groupId)

    // 如果有任何权限（编辑或查询）都允许显示
    return !!access?.permission_points.some(
      point => point === 'EDIT' || point === 'QUERY',
    )
  })

  const handleGroup = async (appList?: T[]) => {
    const arr = cloneDeep(groupList || [])
    const noGroup = {
      applicationType: localize('group.no_group', '未分组'),
      id: noGroupId,
      name: localize('group.no_group', '未分组'),
      sort: '999999999',
      workspaceId: 'currentWorkspaceId',
      children: [] as T[],
    }

    const list = appList || originList || []
    list.forEach(item => {
      const targetIndex = arr.findIndex(i => i.id === (item as any)[groupIdKey])
      if (targetIndex >= 0) {
        arr[targetIndex].children = [
          ...(arr[targetIndex]?.children || []),
          item,
        ] as any
      } else {
        noGroup.children.push(item)
      }
    })

    // 过滤有权限的组并设置
    const fullList = [
      ...arr?.sort((a, b) => a.sort - b.sort),
      noGroup,
    ] as GroupType[]
    const filteredList = fullList.filter(
      group => group.id === noGroupId || checkGroupPermission(group.id),
    )

    setToolList(filteredList)
  }

  useEffect(() => {
    handleGroup()
  }, [appResourceAcl, groupList, originList])

  return {
    toolList,
  }
}
