import { useEffect, useState } from 'react'
import { getAppResourceAcl } from '@apis/acl'
import type {
  AppResourceAclQueryParams,
  AppResourceAclType,
} from '@apis/acl/type'
import { useUserStore } from '../store/user'

export default function useResourceAcl({
  appResourceType,
  workspaceId,
}: {
  appResourceType: AppResourceAclQueryParams['app_resource_type']
  workspaceId: string
}) {
  const [aclData, setAclData] = useState<AppResourceAclType | null>(null)
  const { user, getUser } = useUserStore()
  const userId = user?.userId

  async function getResourceAcl() {
    if (!workspaceId) {
      return
    }

    if (!userId) {
      getUser()
      return
    }

    const appResourceAcl = await getAppResourceAcl({
      user_id: userId,
      app_resource_type: appResourceType,
      workspace_id: workspaceId,
    })

    setAclData(appResourceAcl)
  }

  useEffect(() => {
    getResourceAcl()
  }, [userId, appResourceType, workspaceId])

  return aclData
}
